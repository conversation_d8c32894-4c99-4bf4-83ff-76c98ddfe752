{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //改由js处理
    ////锁定转单发货
    //{
    //  "id": "lock_fratio",
    //  "expression": "field:fratio,fdutyid,famount_ed$*;|fismain==true"
    //},
    ////解锁转单发货
    //{
    //  "id": "unlock_fratio",
    //  "expression": "field:*$fratio,fdutyid,famount_ed;|fismain==false"
    //},
    //锁定转单发货
    {
      "id": "lock_transfership",
      "expression": "menu:btntransfership,btnuntransfership$|fstatus!='E'"
    },
    //解锁转单发货
    {
      "id": "unlock_transfership",
      "expression": "menu:$btnuntransfership,btnuntransfership|fstatus=='E' "
    },
    //此项规则表示：订单关闭 = 已关闭，所有字段不可用,只有 新增 复制 订单关闭 订单反关闭 操作可用，其他操作都不可用
    //2023年3月21日调整：在此规则下，允许【联查】【查看操作日志】操作
    {
      "id": "fclosestate_1",
      "expression": "field:*$;menu:*$tbNew,tbCopy,tbOrderUnClose,tbLinkFormSearch,tbOperateLogs|fclosestate=='1'"
    },
    //此项规则表示：订单反关闭 = 未关闭 时所有字段可用
    {
      "id": "fclosestate_0",
      "expression": "field:$*;menu:tbOrderUnClose$*|fclosestate!='1'"
    },
    //锁定订单后 锁定锁定订单按钮
    {
      "id": "lock_tbLockOrder",
      "expression": "menu:tbLockOrder$tbUnlockOrder|flockstate=='1'"
    },
    //解锁订单后 锁定解锁订单按钮
    {
      "id": "unlock_tbLockOrder",
      "expression": "menu:tbUnlockOrder$tbLockOrder|flockstate!='1'"
    },
    {
      "id": "lock_autoGenerateOrder",
      "expression": "field:*$fstaffid,fpiecesendtag|fisautogenerate==true"
    },
    //此项规则表示：锁定订单后，只有对应【销售员】的用户可以操作。 所有字段不可用，保存 审核 提交 选单 操作不可用，其他操作可用
    {
      "id": "flockstate_1",
      "expression": "field:*$;menu:tbSave,tbSubmit,tbAudit,tbPull$*|flockstate =='1' and fisitlocked !='0' "
    },
    //锁定受理按钮
    {
      "id": "lock_tbAccept",
      "expression": "menu:tbAccept$tbCancelAccept|facceptstatus=='1'"
    },
    //解锁受理按钮
    {
      "id": "unlock_tbAccept",
      "expression": "menu:tbCancelAccept$tbAccept|facceptstatus!='1'"
    },
    //如果有生成出入库记录，则锁定所有字段
    {
      "id": "lock_allfield",
      "expression": "field:*|fstatus=='D' or fstatus=='E'"
    },
    //{
    //  "id": "lock_tbReceiptAndtbRefund",
    //  "expression": "menu:tbStockOut,tbPushPurOrder,tbReturnGood,tbPushVisit,tbPushAfterService$*|fstatus!='E'"
    //},
    //审核状态可以修改交货日期
    {
      "id": "unlock_fdeliverydate",
      "expression": "field:$fdeliverydate|fstatus=='E'"
    },

    //审核状态可以预留
    {
      "id": "unlock_tbReserveInventory",
      "expression": "menu:$tbReserveInventory|fstatus=='E'"
    },
    //非审核状态不可以预留
    {
      "id": "lock_tbReserveInventory",
      "expression": "menu: tbReserveInventory$|fstatus!='E'"
    },

    ////成本核算按钮的鎖定与放開
    //  {
    //    "id": "lock_cost",
    //    "expression": "other:.cost-buttons$|fstatus!='E'"

    //  },
    //{
    //  "id": "unlock_cost",
    //  "expression": "other:$.cost-buttons|fstatus=='E'"

    //},
    // //变更后
    // {
    //   "id": "changing_lock_btns",
    //   "expression": "menu:$tbUnChange,tbSubmitChange|fstatus=='B' and fchangestatus=='1'"
    // },
    //变更中锁定采购按钮
    {
      "id": "lock_tbPushPurOrder",
      "expression": "menu:tbPushPurOrder$|fchangestatus=='1'"
    },
    //变更中锁定收款按钮，暂去掉tbReceipt锁定,
    {
      "id": "lock_tbReceipt",
      "expression": "menu:tbRefund,tbIncomeDisburse$|fchangestatus=='1'"
    },
    //变更中锁定出库按钮
    {
      "id": "lock_tbStockOut",
      "expression": "menu:tbStockOut,tbReturnGood,tbBorrowGood$|fchangestatus=='1'"
    },
    //变更中锁定商品明细属性 商品关闭状态：4手动关闭 3自动关闭
    {
      "id": "fclosestatus_e_0",
      "expression": "field:fprice,fbizqty,fdistrate,fdealprice,fdealamount_e,fqty,funstdtype$|fchangestatus=='1' and (fclosestatus_e=='3' or fclosestatus_e=='4')"
    },

    // 如果订单的【变更状态】=“变更中”或“变更已提交”时：则 <选单><订单关闭><订单反关闭><作废><反作废><预留><手动释放><释放借货><提交>按钮需灰色，禁止点击
    {
      "id": "lock_menu_bychangestatus",
      "expression": "menu:tbPull,tbOrderClose,tbOrderUnClose,tbCancel,tbUncancel,tbReserveInventory,tbManualRelease,tbReserveBorrow,tbSubmit$|fchangestatus=='1' or fchangestatus=='3'"
    },

    //锁定反审核按钮
    //{
    //  "id": "lock_tbUnaudit",
    //  "expression": "menu:tbUnaudit$|fcostid!='' and fcostid!=' '"
    //},
    //解锁反审核按钮
    //{
    //  "id": "unlock_tbUnaudit",
    //  "expression": "menu:$tbUnaudit|fcostid=='' or fcostid==' '"
    //},
    //此规则表示：该商品对应的商品商品属性“允许定制”=true并且已采购数量+销售已发数+销售已出库数=0时放开，为false时，锁定
    {
      "id": "lock_fcustomdesc",
      "expression": "field:fcustomdes_e$|fcustom!=true or fbizpurqty!=0 or fbizdeliveryqty!=0 or fbizoutqty!=0"
    },
    {
      "id": "unlock_fcustomdesc",
      "expression": "field:$fcustomdes_e|fcustom==true and fbizpurqty==0 and fbizdeliveryqty==0 and fbizoutqty==0"
    },
    //此规则表示：该商品对应的商品商品属性“允许选配”=true时，辅助属性放开，为false时，锁定
    //辅助属性不可编辑，去除此判断
    //{
    //  "id": "lock_fsel",
    //  "expression": "field:fattrinfo$|fispresetprop!=true"
    //},
    //{
    //  "id": "unlock_fsel",
    //  "expression": "field:$fattrinfo|fispresetprop==true"
    //},
    {
      "id": "lock_fisoutspot",
      "expression": "field:fisoutspot|fsourcetype=='ydj_saleintention' and fsourceentryid!=''"
    },
    {
      "id": "unlock_fisoutspot",
      "expression": "field:$fisoutspot|fsourcetype!='ydj_saleintention' or fsourceentryid==''"
    },
    {
      "id": "lock_fownerid",
      "expression": "field:fownerid$|fownertype=='' or fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_fownerid",
      "expression": "field:$fownerid|fownertype!='' and fstatus!='D' and fstatus!='E'"
    },
    //解锁总部下单按钮
    {
      "id": "lock_tbpushhqder",
      "expression": "menu:tbpushhqder$|fstatus!='E'"
    },
    //锁定总部下单按钮
    {
      "id": "unlock_tbpushhqder",
      "expression": "menu:$tbpushhqder$|fstatus=='E'"
    },
    {
      "id": "lock_tbPushRegistfee",
      "expression": "menu:tbPushRegistfee|(fmanagemodel=='0' or fmanagemodel == '' or fmanagemodel == ' ' ) and (fchannel=='' or id=='')"
    },
    {
      "id": "unlock_tbPushRegistfee",
      "expression": "menu:$tbPushRegistfee|(fmanagemodel=='0' or fmanagemodel == '' or fmanagemodel == ' ' ) and fchannel!='' and id!=''"
    },
    {
      "id": "lock_tbPushRegistfeeDirectSale",
      "expression": "menu:tbPushRegistfee|fmanagemodel=='1' and (fchannel=='' or fsharecostbillno == '' or id=='' or fclosestatus != '1')"
    },
    {
      "id": "unlock_tbPushRegistfeeDirectSale",
      "expression": "menu:$tbPushRegistfee|fmanagemodel=='1' and fchannel!='' and fsharecostbillno != '' and id!='' and fclosestatus=='1'"
    },
    //直营经销商协同SAP状态控制订单关闭按钮 - 仅针对直营增加额外限制：当经营类型=直营且协同SAP状态=已锁单或已终审时，不允许操作
    {
      "id": "lock_tbOrderClose_DirectSale_SAPStatus",
      "expression": "menu:tbOrderClose$|fmanagemodel=='1' and (fchstatus=='1' or fchstatus=='3')"
    },
    //直营经销商协同SAP状态控制订单反关闭按钮 - 仅针对直营增加额外限制：当经营类型=直营且协同SAP状态=已锁单或已终审时，不允许操作
    {
      "id": "lock_tbOrderUnClose_DirectSale_SAPStatus",
      "expression": "menu:tbOrderUnClose$|fmanagemodel=='1' and (fchstatus=='1' or fchstatus=='3')"
    },
    {
      "id": "fstatus_",
      "expression": "field:$*;menu:*$tbNew,tbSave,tbPull,tbSaveSubmit,tbSaveAudit,tbQueryInventory,tbQueryFirstInventory,btnquerybarcode,btnUnStandardCustom,btnStandardCustom,btnSuitCustom,tbUnstdTypeAudit,tbTransferOrderApply,btnQYG,btnZCG,btnCXDB,btnbindcombnumber,btnunbindcombnumber,btnresetsort,btnShowSelection,btnSelectProductPromotion,btnSelectComboPromotion,btnAlterHqPrice|fstatus=='' and fbilltype!='ydj_order_vsix'"
    },
    //保存并提交锁定&解锁
    {
      "id": "lock_tbSaveSubmit",
      "expression": "menu:tbSaveSubmit$|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_tbSaveSubmit",
      "expression": "menu:$tbSaveSubmit|fstatus!='D' and fstatus!='E'"
    },
    //保存并审核锁定锁定&解锁
    {
      "id": "lock_tbSaveAudit",
      "expression": "menu:tbSaveAudit$|fstatus=='E'"
    },
    {
      "id": "unlock_tbSaveAudit",
      "expression": "menu:$tbSaveAudit|fstatus!='E'"
    },
    {
      "id": "lock_btnShowSelection",
      "expression": "menu:btnShowSelection$|fstatus=='E'"
    },
    //非标审批锁定
    {
      "id": "lock_tbUnstdTypeAudit",
      "expression": "menu:tbUnstdTypeAudit$|fstatus=='D' or fstatus=='E' or fcancelstatus==true and fbilltype!='ydj_order_vsix'"
    },
    {
      "id": "unlock_tbUnstdTypeAudit",
      "expression": "menu:$tbUnstdTypeAudit|fstatus!='D' and fstatus!='E' and fcancelstatus==false and fbilltype!='ydj_order_vsix'"
    },
    //绑定沙发组合号锁定
    {
      "id": "lock_btnbindcombnumber",
      "expression": "menu:btnCXDB,btnZCG,btnQYG,btnUnStandardCustom,btnStandardCustom,btnSuitCustom,btnbindcombnumber,btnunbindcombnumber,btnAlterHqPrice$|fstatus=='D' or fstatus=='E' or fcancelstatus==true and fbilltype!='ydj_order_vsix'"
      //"expression": "menu:tbTransferOrderApply,btnCXDB,btnZCG,btnQYG,btnUnStandardCustom,btnStandardCustom,btnSuitCustom,btnbindcombnumber,btnunbindcombnumber$|fstatus=='D' or fstatus=='E' or fcancelstatus==true"
    },
    {
      "id": "unlock_btnbindcombnumber",
      "expression": "menu:$btnCXDB,btnZCG,btnQYG,btnUnStandardCustom,btnStandardCustom,btnSuitCustom,btnbindcombnumber,btnunbindcombnumber,btnAlterHqPrice|fstatus!='D' and fstatus!='E' and fcancelstatus==false and fbilltype!='ydj_order_vsix'"
      //"expression": "menu:$tbTransferOrderApply,btnCXDB,btnZCG,btnQYG,btnUnStandardCustom,btnStandardCustom,btnSuitCustom,btnbindcombnumber,btnunbindcombnumber|fstatus!='D' and fstatus!='E' and fcancelstatus==false"
    },
    //通过转单接口生成的销售合同锁定以下明细按钮
    {
      "id": "lock_entryButton",
      "expression": "menu:btnStandardCustom,btnUnStandardCustom,btnSuitCustom,tbUnstdTypeAudit,tbTransferOrderApply,btnQYG,btnZCG,btnCXDB,btnbindcombnumber,btnunbindcombnumber$|fissaletransferorder==true"
    },
    { //作废时锁定相关按钮
      "id": "lock_buttonByfcancelstatus",
      "expression": "menu:tbBatchmodify,tbOpenInvoice,tbLockOrder,tbSubmitChange,tbAccept,tbSave,tbPull,tbOrderClose,tbReserveInventory,tbReceipt,tbManualRelease,tbReserveBorrow,tbPushPurOrder,tbStockOut,tbfollowerrecord,tbPushFeedback,tbRefund,tbIncomeDisburse,tbReturnGood,tbPushVisit,tbpushaftrepairorder,tbBorrowGood$|fcancelstatus==true"
    },
    {
      "id": "unlock_buttonByfcancelstatus",
      "expression": "menu:$tbBatchmodify,tbOpenInvoice,tbLockOrder,tbSubmitChange,tbAccept,tbSave,tbPull,tbOrderClose,tbReserveInventory,tbReceipt,tbManualRelease,tbReserveBorrow,tbPushPurOrder,tbStockOut,tbfollowerrecord,tbPushFeedback,tbRefund,tbIncomeDisburse,tbReturnGood,tbPushVisit,tbpushaftrepairorder,tbBorrowGood|fcancelstatus==false"
    },
    //促销活动锁定&解锁
    {
      "id": "lock_promotion",
      "expression": "menu:btnSelectProductPromotion,btnSelectComboPromotion$|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_promotion",
      "expression": "menu:$btnSelectProductPromotion,btnSelectComboPromotion|fstatus!='D' and fstatus!='E'"
    },
    //订单类型锁定&解锁
    //{
    //  "id": "lock_indenttype",
    //  "expression": "field:findenttype$|1==1"
    //},
    //{
    //  "id": "unlock_indenttype",
    //  "expression": "field:$findenttype|1==1"
    //},
    //第三方来源 锁定&解锁
    {
      "id": "lock_thirdsource",
      "expression": "field:fthirdsource$|ffactorybillid!=''"
    },
    {
      "id": "unlock_thirdsource",
      "expression": "field:$fthirdsource|ffactorybillid==''"
    },
    //重新提交OMS锁定
    {
      "id": "lock_submitomsagain",
      "expression": "menu:btnsubmitomsagain$|fstatus=='D' or fstatus=='E' or (fchangestatus=='1' or fchangestatus=='3')"
    },
    {
      "id": "lock_tbStockOutDirectSale",
      "expression": "menu:tbStockOut$|fmanagemodel=='1' and (fchstatus == '' or fchstatus == '1' or fchstatus == '2' or (fchstatus == '3' and fpiecesendtag == true))"
    },
    {
      "id": "unlock_tbStockOutDirectSale",
      "expression": "menu:$tbStockOut|fmanagemodel=='1' and fchstatus == '3' and fpiecesendtag == false"
    },
    {
      "id": "lock_tbUnCloseDirectSale",
      "expression": "menu:tbBizClose,tbUnClose$|fmanagemodel=='1' and (fchstatus == '1' or fchstatus == '3')"
    },
    {
      "id": "unlock_tbUnCloseDirectSale",
      "expression": "menu:$tbBizClose,tbUnClose|fmanagemodel=='1' and (fchstatus == '' or fchstatus == '2' or fchstatus=='4')"
    }

    ////单据类型】=”v6定制柜合同“，商品行上按钮仅能点击<关闭><反关闭><非标定制>按钮
    //{
    //  "id": "unlock_menuBtnVSix",
    //  "expression": "field:fproductid,fqty,fbizqty$;menu:btnShowSelection,btnStandardCustom,btnSuitCustom,btnUnStandardCustom,tbUnstdTypeAudit,tbTransferOrderApply,btnQYG,btnZCG,btnCXDB,btnbindcombnumber,btnunbindcombnumber,btnquerybarcode,tbQueryInventory,tbBizClose,tbUnClose$|fbilltype__fname=='v6定制柜合同'"
    //},
    ////（三维家）【单据类型】=”v6定制柜合同“不允许操作任何按钮
    //{
    //  "id": "lock_menuBtnVSix",
    //  "expression": "field:$fproductid;menu:$btnShowSelection,btnStandardCustom,btnSuitCustom,btnUnStandardCustom,tbUnstdTypeAudit,tbTransferOrderApply,btnQYG,btnZCG,btnCXDB,btnbindcombnumber,btnunbindcombnumber,btnquerybarcode,tbQueryInventory,tbBizClose,tbUnClose|fbilltype__fname!='v6定制柜合同'"
    //}
    //,
    //{
    //  "id": "lock_fcustomercontact",
    //  "expression": "other:[opcode='selectCus']$|fstatus=='D' or fstatus=='E' or fchangestatus=='1'"
    //},
    //{
    //  "id": "unlock_fcustomercontact",
    //  "expression": "other:$[opcode='selectCus']|fstatus!='D' and fstatus!='E' and fchangestatus!='1'"
    //}
  ],

  //定义表单可见性规则
  "visibleRules": [
    //非提交审核状态下显示隐藏量尺记录相关操作
    {
      "id": "hide_lookscale",
      "expression": "other:[opcode='lookscale'],.assignscale_s$|fscalerecord=='' or fscalerecord==' ' and (fstatus!='E')"
    },
    {
      "id": "show_lookscale",
      "expression": "other:.assignscale_a$[opcode='lookscale'],.assignscale_s|fscalerecord!='' and fscalerecord!=' ' and (fstatus!='E')"
    },
    //非提交审核状态下显示隐藏设计方案相关操作
    {
      "id": "hide_lookscheme",
      "expression": "other:[opcode='lookdesigner'],.assigndesigner_s$|fdesignscheme=='' or fdesignscheme==' ' and (fstatus!='E')"
    },
    {
      "id": "show_lookscheme",
      "expression": "other:.assigndesigner_a$[opcode='lookdesigner'],.assigndesigner_s|fdesignscheme!='' and fdesignscheme!=' ' and (fstatus!='E')"
    },
    //当量尺记录和设计方案没有数据且为已提交审核状态时
    {
      "id": "hide_ydesign",
      "expression": "other:.look-scheme,.assigndesigner_a,[opcode='lookdesigner']$.y-design|(fdesignscheme=='' or fdesignscheme==' ') and (fstatus=='E')"
    },
    {
      "id": "show_yscale",
      "expression": "other:.look-scale,.assignscale_a,[opcode='lookscale']$.y-scale|(fscalerecord=='' or fscalerecord==' ') and (fstatus=='E')"
    },
    //审核状态隐藏多余的指派设计和指派量尺
    {
      "id": "hide_assigndesigner_E",
      "expression": "other:.assigndesigner_a$|(fdesignscheme!='' or fdesignscheme!=' ') and (fstatus=='E')"
    },
    {
      "id": "hide_assignscale_E",
      "expression": "other:.assignscale_a$|(fscalerecord!='' or fscalerecord!=' ') and (fstatus=='E')"
    },
    //{
    //  "id": "hide_draw",
    //  "expression": "other:.draw-info$|fbilltype!='order_billtype_02'"
    //},
    //{
    //  "id": "show_draw",
    //  "expression": "other:$.draw-info|fbilltype=='order_billtype_02'"
    //},
    {
      "id": "hide_v6info",
      "expression": "other:.v6-info,.draw-info$|fbilltype!='ydj_order_vsix'"
    },
    {
      "id": "show_v6info",
      "expression": "other:$.v6-info,.draw-info|fbilltype=='ydj_order_vsix'"
    }
    //{
    //  "id": "hide_swjv6info",
    //  "expression": "field:fswjordernumber,ffactorybillno,fsendtarget,fswjdesignerid$|fbilltype__fname!='v6定制柜合同'"
    //},
    //{
    //  "id": "show_swjv6info",
    //  "expression": "field:$fswjordernumber,ffactorybillno,fsendtarget,fswjdesignerid|fbilltype__fname=='v6定制柜合同'"
    //},
    //{
    //  "id": "hide_swjv6info",
    //  "expression": "other:.v6-swj-info$|fbilltype__fname!='v6定制柜合同'"
    //},
    //{
    //  "id": "show_swjv6info",
    //  "expression": "other:$.v6-swj-info|fbilltype__fname=='v6定制柜合同'"
    //}

  ],

  //定义表单计算规则
  "calcRules": [
    //选择导购员信息，自动带出门店
    //{ "expression": "fdeptid=fstaffid__fdeptid|fstaffid=='' or 1==1" },
    //选择销售部门，自动带出仓库
    //{ "expression": "fstorehouseid=fdeptid_e__fstorehouseid" },

    //客户基础资料值变化时，携带客户属性字段到页面指定的字段上面
    //{ "expression": "fprovince=fcustomerid__fprovince" },
    //{ "expression": "fcity=fcustomerid__fcity" },
    //{ "expression": "fregion=fcustomerid__fregion" },
    //{ "expression": "flinkstaffid=fcustomerid__fcontacts" },
    //{ "expression": "faddress=fcustomerid__faddress" },
    //{ "expression": "fphone=fcustomerid__fphone" },
    { "expression": "fbuildingid=fcustomerid__fbuildingid|fcustomerid!=''" },
    { "expression": "fcustomersumamount=fcustomerid__fsumamount|fcustomerid!=''" },
    { "expression": "fcoophone=fterminalcustomer__fphone" },
    { "expression": "fterminalcustomernumber=fterminalcustomer__fnumber" },
    { "expression": "fcontacts_c=fterminalcustomer__fcontacts" },
    { "expression": "fcooaddress=fterminalcustomer__faddress" },
    { "expression": "fprovince_c=fterminalcustomer__fprovince" },
    { "expression": "fcity_c=fterminalcustomer__fcity" },
    { "expression": "fregion_c=fterminalcustomer__fregion" },
    { "expression": "fteramount=fbizqty*fterprice" },

    //单据头【单据类型】 = "大客户销售合同" 且 单据头【客户】为空时 合作渠道带出-收货人-手机号-省市区-详细地址
    { "expression": "flinkstaffid=fchannel__fcontacts|fcustomerid=='' and fbilltype__fname=='大客户销售合同'" },
    { "expression": "faddress=fchannel__faddress|fcustomerid=='' and fbilltype__fname=='大客户销售合同'" },
    { "expression": "fphone=fchannel__fphone|fcustomerid=='' and fbilltype__fname=='大客户销售合同'" },
    { "expression": "fprovince=fchannel__fprovince|fcustomerid=='' and fbilltype__fname=='大客户销售合同'" },
    { "expression": "fcity=fchannel__fcity|fcustomerid=='' and fbilltype__fname=='大客户销售合同'" },
    { "expression": "fregion=fchannel__fregion|fcustomerid=='' and fbilltype__fname=='大客户销售合同'" },
    //单据头【单据类型】 = "大客户销售合同" 且 单据头【客户】为空时 合作渠道带出-收货人-手机号-省市区-详细地址
    { "expression": "fchanneltype=fchannel__ftype" },

    { "expression": "fchannel=fcustomerid__fchannelid|fcustomerid!='' and fcustomerid__fchannelid.fname!=''" },
    //反写未收，未收 = 订单总额 - 确定已收，启用参数：销售合同实退不体现未收
    { "expression": "funreceived=fsumamount-freceivable|fdontreflect==true" },
    //反写未收，未收 = 订单总额 - 确定已收-申请退货金额，不启用参数：销售合同实退不体现未收
    { "expression": "funreceived=fsumamount-freceivable-frefundamount|fdontreflect==false" },

    //性能优化：单位的修改会导致前端重新取价，导致性能问题，这里去掉销售单位的自动携带，在js代码中通过setvalue设置（增加参数标明不需要取价 tgFatchPrice: false），
    //{ "expression": "funitid=fproductid__funitid|fproductid=='' or 1==1" },
    // { "expression": "fbizunitid=fproductid__fsalunitid|fproductid=='' or 1==1" },
    { "expression": "fsupplierid=fproductid__fsupplierid|fproductid=='' or 1==1" },
    //带出商品是否非标
    { "expression": "funstdtype=fproductid__funstdtype|fproductid!='' or 1==1" },

    { "expression": "fbizqty=1|fproductid!='' and fbizqty<=0" },
    { "expression": "fqty=1|fproductid!='' and fqty<=0" },
    { "expression": "fdistrate=10|fproductid!='' and fdistrate<=0" },
    { "expression": "famount=fprice*fbizqty" },
    { "expression": "fdealamount_e=fdealprice*fbizqty|fisgiveaway==false" },
    { "expression": "fcost=fcostprice*fbizqty" },
    { "expression": "fdealprice=fprice*fdistrate/10|(fbizoutqty<=0 or fpromotioncombono!='') and fisgiveaway==false" },
    { "expression": "fdistamount_e=famount-fdealamount_e" },
    { "expression": "fdistrate=fdealprice*10/fprice|fprice!=0 and fprice!=''" },
    { "expression": "fdealprice=fdealamount_e/fbizqty|(fbizoutqty<=0 or fpromotioncombono!='') and fbizqty!=0 and fisgiveaway==false" },
    //{ "expression": "fattrinfo=getAuxPropValue(fproductid)" },
    { "expression": "fprice=famount/fbizqty|fbizqty!=0" },
    { "expression": "fmtrlimage=getImages(fproductid,fattrinfo,fcustomdes_e)" },
    { "expression": "finnercustomerid=fdeptid__flinkcustomerid|fdeptid!='' or 1==1" },
    { "expression": "fdeptid_e=fstaff_e__fdeptid_e|fstaff_e=='' or 1==1" },
    { "expression": "fresultbrandid=fproductid__fseriesid|fproductid!='' and fdeptid=='' " },
    { "expression": "fpurfacamount=fpurfacprice*fbizqty" },
    { "expression": "fpurfacprice=fpurfacamount/fbizqty|fbizqty!=0" },
    // 已废弃
    // 经销总价 = 经销价 * 销售数量
    //{ "expression": "fsellamount=fsellprice*fbizqty" },
    // 整单经销总价 = 所有商品明细行的经销总价汇总
    //{ "expression": "fsumsellamount=sum(fsellamount)" },

    { "expression": "fstore=fdeptid__fstore" },
    //销售员信息 销售员带出部门
    { "expression": "fdeptid_ed=fdutyid__fdeptid" },
    { "expression": "fordernumber=fwithin" },
    //仓库带出库存状态
    { "expression": "fstockstatus=fstorehouseid__fstockid|fstorehouseid!=''" },
    //客户类型
    { "expression": "fcustomertype=fcustomerid__ftype|fcustomerid!=''" },
    //业务数量变化时计算未出库基本数量
    { "expression": "funstockoutqty=fbizqty-fbizoutqty+fbizreturnqty|fbizqty!='' or fbizoutqty!='' or fbizreturnqty!=''" },
    //总部折扣率=成交单价/总部零售价；
    { "expression": "fhqdistrate=fdealprice/fhqprice|fhqprice!=0 and fisresellorder==false" }
  ]
}