using JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.Stock.AppService.Common;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.Framework.MetaCore.Validator;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.Framework.Utils;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：审核
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("audit")]
    public class Audit : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "fmaterialid", "fstorehouseid", "fstoreto", "fstorein" });
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null)
                return;
            var nowDate = DateTime.Now;
            foreach (var item in e.DataEntitys)
            {
                item["fiscallout"] = true;//是否做出调出操作
                bool isstockout = Convert.ToBoolean(item["fisstockout"]);//已分步式调出
                if (isstockout)
                {
                    var entitys = item["fentity"] as DynamicObjectCollection;
                    List<string> materialnos = entitys.Select(o => o["fmaterialid"]?.ToString()).ToList();
                    var allMaterials = this.Context.LoadBizDataById("ydj_product", materialnos, true);
                    foreach (var en in entitys)
                    {
                        if (Convert.ToDecimal(en["fstockinqty"]) == 0 && Check(item["fbillno"]?.ToString()))
                        {
                            en["fstockinqty"] = en["fstockoutqty"];//分步式调入数量

                            decimal fstockinbizqty = UnitConvert(en["funitid"]?.ToString(), Convert.ToDecimal(en["fstockoutqty"]), en["fbizunitid"]?.ToString(), allMaterials, en["fmaterialid"]?.ToString());

                            en["fstockinbizqty"] = fstockinbizqty;//分步式调入基本单位数量
                        }
                    }
                }
                //task 70392,【库存调拨单】以当前日期更新至【调拨日期】
                item["fdate"] = nowDate;
            }
        }


        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            var result = this.Container.GetService<IReserveUpdateService>()
                 .FinishTransferBillReserve(this.Context, this.HtmlForm, e.DataEntitys, this.Option);
            this.Result.MergeResult(result);

            new OrderCommon(this.Context).AlterOrderCloseStatus(e.DataEntitys, this.OperationNo);
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmptyOrWhiteSpace() || !e.DataEntitys.Any())
            {
                return;
            }

            string mainorgid = this.Context.Company;
            IDBService service = this.DBService;
            foreach (var item in e.DataEntitys)
            {
                string sourcebillno = Convert.ToString(item["fsourcenumber"]);

                IndbqtyUpdateHelper.ndbqtyUpdate(this.Context, mainorgid, sourcebillno, service);
            }

            SyncInvTransfer(e.DataEntitys);

        }

        /// <summary>
        /// 单位换算
        /// </summary>
        /// <param name="fbizunitid">单位</param>
        /// <param name="tupqty"></param>
        /// <param name="funitid">基本单位</param>
        /// <param name="allMaterials"></param>
        /// <param name="materialid13"></param>
        /// <returns></returns>
        public decimal UnitConvert(string fbizunitid, decimal tupqty, string funitid, List<DynamicObject> allMaterials, string materialid13)
        {
            decimal fqty = UnitConvertHelper.UnitConvertToBaseById(this.Context, allMaterials, materialid13, fbizunitid, tupqty, funitid);
            return fqty;
        }

        /// <summary>
        /// 判断是否存在调出扫描任务以及任务状态是否为"已完成"
        /// </summary>
        /// <param name="srcBillNo"></param>
        /// <returns></returns>
        public bool Check(string srcBillNo)
        {
            bool flag = false;
            var sql = $"select ftaskstatus from t_bcm_transfertask where ftask_type = 'transferout' and fsourcenumber = '{srcBillNo}'";
            var data = this.DBService.ExecuteDynamicObject(this.Context, sql).FirstOrDefault();
            if (data == null)
            {
                flag = true;
            }
            else
            {
                //任务状态"已完成"
                string ftaskstatus = data["ftaskstatus"]?.ToString();
                if (ftaskstatus != "ftaskstatus_04")
                {
                    flag = true;
                }
            }
            return flag;
        }

        /// <summary>
        /// 发送调拨单至中台
        /// </summary>
        /// <param name="order"></param>
        /// <param name="syncingEntrys"></param>
        private void SyncInvTransfer(DynamicObject[] order)
        {
            //直营并且不等于跨组织调出，走同步接口
            var zyOrder = order.Where(a => Convert.ToString(a["fmanagemodel"]).Equals("1") && !Convert.ToString(a["ftransfertype"]).Equals("invtransfer_biztype_04"));
            if (zyOrder == null || zyOrder.Count() == 0) return;

            string storehouseNumber = "";
            string _sql = $"select top 1 fid,fnumber from T_YDJ_STOREHOUSE with(nolock) where  fmainorgid='{this.Context.Company}' and fwarehousetype='warehouse_01' and fforbidstatus=0  and fcreatorid='sysadmin' order by fcreatedate asc";
            using (var dr = this.Context.ExecuteReader(_sql, null))
            {
                if (dr.Read())
                {
                    storehouseNumber = Convert.ToString(dr["fnumber"]);
                }
            }
            //当调出仓库的门店与调入仓库的门店一致，不走接口

            var agentItem = this.Context.LoadBizDataById("bas_agent", this.Context.Company);
            Dictionary<string, object> dicts = new Dictionary<string, object>();
            var mate = this.Context.Container.GetService<IMetaModelService>();
            var agentForm = mate.LoadFormModel(this.Context, "bas_agent");
            var matForm = mate.LoadFormModel(this.Context, "ydj_product");
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, new DynamicObject[] { agentItem }, true, agentForm, new List<string> { "fsaleorgid" });

            List<string> soOrderId = new List<string>();
            List<string> matIds = new List<string>();
            zyOrder.ForEach(a =>
            {
                var entity = a["fentity"] as DynamicObjectCollection;
                soOrderId.AddRange(entity.Select(b => Convert.ToString(b["fsourceinterid"])));
                matIds.AddRange(entity.Select(b => Convert.ToString(b["fmaterialid"])));
            });
            soOrderId = soOrderId.Distinct().ToList();
            matIds = matIds.Distinct().ToList();
            var salOrders = this.Context.LoadBizDataById("ydj_order", soOrderId);
            var matObjs = this.Context.LoadBizDataById("ydj_product", matIds);
            if (salOrders.Count > 0)
            {
                var formModel = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                // 加载数据
                refObjMgr?.Load(this.Context, salOrders.ToArray(), true, formModel, new List<string> { "fstore" });
            }
            refObjMgr?.Load(this.Context, matObjs.ToArray(), true, agentForm, new List<string> { "funitid", "fcategoryid" });
            //foreach (var orderItem in zyOrder)
            //{
            //    var dto = BuildData(orderItem, salOrders, agentItem, matObjs);
            //    var resp = MuSiApi.SendInvTransfer(this.Context, this.HtmlForm, dto);

            //    orderItem["fhqderdate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            //    orderItem["fhqderstatus"] = "1";//已发送
            //}
            //当调出仓库的门店与调入仓库的门店一致，不走接口
            foreach (var orderItem in zyOrder)
            {
                var outStore = Convert.ToString(orderItem["fstore"]);
                var inStore = Convert.ToString(orderItem["fstoreto"]);
                var entitys = orderItem["fentity"] as DynamicObjectCollection;
                bool allSameStore = true;
                //foreach (var entity in entitys)
                //{
                //    // 获取调出仓库和调入仓库的门店编号
                //    var storehouseOut = entity["fstorehouseid_ref"] as DynamicObject;
                //    var storehouseIn = entity["fstorehouseidto_ref"] as DynamicObject;
                //    var outStore = storehouseOut != null ? Convert.ToString(storehouseOut["fmulstore"]) : "";
                //    var inStore = storehouseIn != null ? Convert.ToString(storehouseIn["fmulstore"]) : "";

                //}
                // 若有一行不一致，则需要走接口
                if (outStore != inStore)
                {
                    allSameStore = false;
                }
                if (allSameStore)
                {
                    // 所有明细调出仓库和调入仓库的门店都一致，不走接口
                    continue;
                }

                var dto = BuildData(orderItem, salOrders, agentItem, matObjs,storehouseNumber);
                var resp = MuSiApi.SendInvTransfer(this.Context, this.HtmlForm, dto);

                orderItem["fhqderdate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                orderItem["fhqderstatus"] = "1";//已发送
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(zyOrder);
        }
        /// <summary>
        /// 构建数据包
        /// </summary>
        /// <param name="order"></param>
        /// <param name="fentry"></param>
        /// <returns></returns>
        private Dictionary<string, object> BuildData(DynamicObject order, List<DynamicObject> salOrders, DynamicObject agentItem, List<DynamicObject> matObjs,string storehouseNumber)
        {
            Dictionary<string, object> dicts = new Dictionary<string, object>();
            string transferType = Convert.ToString(order["ftransfertype"]);
            switch (transferType)
            {
                case "invtransfer_biztype_01"://标准
                    transferType = "01";
                    break;
                case "invtransfer_biztype_02"://借货
                    transferType = "02";
                    break;
                case "invtransfer_biztype_03"://维修
                    transferType = "03";
                    break;
                case "invtransfer_biztype_04"://跨组织调出
                    transferType = "04";
                    break;
                case "invtransfer_biztype_05"://跨组织调入
                    transferType = "05";
                    break;
            }

            dicts.Add("zyType", transferType);
            dicts.Add("zyTranferNo", Convert.ToString(order["fbillno"]));
            dicts.Add("zySaleOrg", Convert.ToString((agentItem["fsaleorgid_ref"] as DynamicObject)?["fnumber"])); // 公司编码
            dicts.Add("zyComments", Convert.ToString(order["fdescription"]));
            dicts.Add("zyCrtName", Convert.ToString((order["fstockstaffid_ref"] as DynamicObject)?["fname"]));
            if (transferType.Equals("05"))//跨组织调入
            {
                dicts.Add("zyCrtDate", Convert.ToDateTime(order["fcreatedate"]).ToString("yyyy-MM-dd HH:mm:ss"));
                dicts.Add("zyDocDate", Convert.ToDateTime(order["fcreatedate"]).ToString("yyyy-MM-dd HH:mm:ss"));
                dicts.Add("zyCurrency", "CNY");
            }
            List<Dictionary<string, object>> entityDics = new List<Dictionary<string, object>>();
            var entitys = order["fentity"] as DynamicObjectCollection;
            foreach (var item in entitys)
            {
                var orderItem = salOrders.FirstOrDefault(a => Convert.ToString(a["id"]) == Convert.ToString(item["fsourceinterid"]));
                DynamicObject ordEntryItem = null;
                if (orderItem != null)
                {
                    var ordEntry = orderItem["fentry"] as DynamicObjectCollection;
                    ordEntryItem = ordEntry.FirstOrDefault(a => Convert.ToString(a["id"]) == Convert.ToString(item["fsourceentryid"]));
                    if (ordEntryItem == null) continue;
                }
                string zyStoreNumOut = Convert.ToString((order["fstore_ref"] as DynamicObject)?["fnumber"]);
                string zyStoreNumIn = Convert.ToString((order["fstoreto_ref"] as DynamicObject)?["fnumber"]);

                var storehouseidtoObj = (item["fstorehouseidto_ref"] as DynamicObject);
                var mulstoreto = Convert.ToString(storehouseidtoObj?["fwarehousetype"]);
                if (storehouseidtoObj != null && Convert.ToString(storehouseidtoObj["fwarehousetype"]).Equals("warehouse_01"))//总仓
                {
                    //总仓传仓库编码
                    //zyStoreNumIn = Convert.ToString((item["fstorehouseidto_ref"] as DynamicObject)?["fnumber"]);
                    zyStoreNumIn = storehouseNumber;
                    
                }
                else if (mulstoreto.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(storehouseidtoObj["fwarehousetype"]).Equals("warehouse_04"))//售后仓
                {
                    //总仓传仓库编码
                    //zyStoreNumIn = Convert.ToString((item["fstorehouseidto_ref"] as DynamicObject)?["fnumber"]);
                    zyStoreNumIn = storehouseNumber;
                }
                else if (!mulstoreto.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(storehouseidtoObj["fwarehousetype"]).Equals("warehouse_04"))//售后单
                {
                    zyStoreNumIn = Convert.ToString((order["fstoreto_ref"] as DynamicObject)?["fnumber"]);
                }
                var storehouseObj = (item["fstorehouseid_ref"] as DynamicObject);
                var mulstore = Convert.ToString(storehouseidtoObj?["fwarehousetype"]);
                if (storehouseObj != null && Convert.ToString(storehouseObj["fwarehousetype"]).Equals("warehouse_01"))//总仓
                {
                    //总仓传仓库编码
                    //zyStoreNumOut = Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fnumber"]);
                    zyStoreNumOut = storehouseNumber;
                }
                else if (mulstore.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(storehouseObj["fwarehousetype"]).Equals("warehouse_04"))//售后仓
                {
                    //总仓传仓库编码
                    //zyStoreNumIn = Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fnumber"]);
                    zyStoreNumOut = storehouseNumber;
                }
                else if (!mulstore.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(storehouseObj["fwarehousetype"]).Equals("warehouse_04"))//售后单
                {
                    zyStoreNumOut = Convert.ToString((order["fstore_ref"] as DynamicObject)?["fnumber"]);
                }
                var marObj = matObjs.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(item["fmaterialid"]))).FirstOrDefault();
                if (marObj == null)
                {
                    marObj = item["fmaterialid_ref"] as DynamicObject;
                }
                var entityDict = new Dictionary<string, object>
                    {
                        { "zyItemLine", Convert.ToString(item["fseq"]) },
                        { "zyPrdCode", Convert.ToString(marObj?["fnumber"]) },
                        { "zyPrdName", Convert.ToString(marObj?["fname"]) },
                        { "zyOrdNumOut", Convert.ToString(orderItem?["fbillno"]) },
                        { "zyItemLineOut",Convert.ToString(ordEntryItem?["fseq"]) },
                        { "zyStoreNumIn",zyStoreNumIn },
                        { "zyQty", Convert.ToString(item["fbizqty"]) },
                        { "zyUnit", Convert.ToString((marObj["funitid_ref"] as DynamicObject)?["fnumber"]) },
                        { "zyComments", Convert.ToString(item["fentrynote"]) }
                    }
            ;
                if (!transferType.Equals("05"))//不等于跨组织调入，才传
                {
                    entityDict.Add("zyStoreNumOut", zyStoreNumOut);
                }
                if (transferType.Equals("05"))//等于跨组织调入，才传
                {
                    entityDict.Add("zyCrtDate", Convert.ToDateTime(order["fcreatedate"]).ToString("yyyy-MM-dd HH:mm:ss"));
                    entityDict.Add("zyMatkl", Convert.ToString((marObj["fcategoryid_ref"] as DynamicObject)?["fnumber"]));
                    entityDict.Add("zyAllotLine", Convert.ToString(item["fseq"]));
                    entityDict.Add("zyPlanLine", Convert.ToString(item["fseq"]));
                    entityDict.Add("zyAllotDate", Convert.ToDateTime(order["fdate"]).ToString("yyyy-MM-dd HH:mm:ss"));
                    entityDict.Add("zyAllotQty", Convert.ToString(item["fbizqty"]));
                    entityDict.Add("zyDelQty", Convert.ToString(item["fbizqty"]));

                }
                if (transferType.Equals("02") || transferType.Equals("03"))
                {
                    entityDict.Add("zyOrdNumIn", Convert.ToString(orderItem?["fbillno"]));
                    entityDict.Add("zyItemLineIn", Convert.ToString(ordEntryItem?["fseq"]));
                }
                entityDics.Add(entityDict);
            }
            dicts.Add("item", entityDics);
            return dicts;
        }

    }
}
