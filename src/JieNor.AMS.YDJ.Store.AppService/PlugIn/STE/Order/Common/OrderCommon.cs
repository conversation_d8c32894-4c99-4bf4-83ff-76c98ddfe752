using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common
{
    /// <summary>
    /// 订单公共部分
    /// </summary>
    public class OrderCommon
    {
        public const string OrderFormId = "ydj_order";
        public const string TransferOrderApplyFormId = "ydj_transferorderapply";
        public const string SaleTransferOrderBillType = "ydj_saletransferorder_01";
        private UserContext Context;
        public static string HasNotShipperAgentMessage = "尚未转单或已转单且不存在需要自行发货的商品！";
        public OrderCommon(UserContext context)
        {
            this.Context = context;
        }

        //检查需转单的商品明细中是否存在发货经销商等于当前登录人
        public void CheckNeedTransferOrderEntryHasShipperAgent(DynamicObject order)
        {
            if (IsNeedTransferOrder(order) && !HasShipperEntry(order))
            {
                throw new BusinessException(HasNotShipperAgentMessage);
            }
        }

        /// <summary>
        /// 商品明细中是否存在发货经销商等于当前登录人
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public bool HasShipperEntry(DynamicObject order)
        {
            var entries = order["fentry"] as DynamicObjectCollection;
            if (entries != null && entries.Count > 0)
            {
                if (entries.Count(x => !Convert.ToString(x["ftransferorderstatus"]).IsNullOrEmptyOrWhiteSpace() && x["fshipperagentid"].ToString() == Context.Company) > 0)
                {
                    return true;
                }
            }
            return false;
        }
        ///// <summary>
        ///// 商品明细中是否存在发货经销商等于当前登录人
        ///// </summary>
        ///// <param name="order"></param>
        ///// <returns></returns>
        //public bool HasShipperEntry(DynamicObject order, IEnumerable<string> selectRowIds)
        //{
        //    var entries = order["fentry"] as DynamicObjectCollection;
        //    if (entries != null && entries.Count > 0)
        //    {
        //        if (entries.Count(x => selectRowIds.Contains(x["id"]) && !Convert.ToString(x["ftransferorderstatus"]).IsNullOrEmptyOrWhiteSpace() && x["fshipperagentid"].ToString() == Context.Company) > 0)
        //        {
        //            return true;
        //        }
        //    }
        //    return false;
        //}

        /// <summary>
        /// 是否转单审核中（批量检查）【采购出库、退货、借货、预留、订单关闭、批改、反审核、作废、变更】
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public void CheckTransferOrderApproving(DynamicObject[] orders)
        {
            List<string> approvingBillNoList = new List<string>();
            foreach (var order in orders)
            {
                var isApproving = IsTransferOrderApproving(order);
                if (isApproving)
                {
                    approvingBillNoList.Add(order["fbillno"].ToString());
                }
            }
            if (approvingBillNoList.Count > 0)
            {
                throw new BusinessException($"销售合同[{string.Join(",", approvingBillNoList)}]中的商品明细存在转单申请单为审批中时无法执行该操作！");
            }
        }



        /// <summary>
        /// 判断下游是否有采购订单（因促销选单可以选择采购订单）
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public void CheckPurOrder(DynamicObject[] orders)
        {
            List<string> approvingBillNoList = new List<string>();
            foreach (var order in orders)
            {
                var isApproving = CheckIsPushPurOrder(Convert.ToString(order["fbillno"]), (order["fentry"] as DynamicObjectCollection).Select(a => Convert.ToString(a["id"])).ToList());
                if (isApproving)
                {
                    approvingBillNoList.Add(order["fbillno"].ToString());
                }
            }
            if (approvingBillNoList.Count > 0)
            {
                throw new BusinessException($"销售合同[{string.Join(",", approvingBillNoList)}]中的商品明细存在下游采购订单，无法执行该操作！");
            }
        }


        /// <summary>
        /// 检查是否已经下推过采购订单
        /// </summary>
        /// <param name="dataEntity"></param>
        private bool CheckIsPushPurOrder(string fbillno, List<string> rows)
        {
            int count = 0;
            string strSql = "";
            ///如果先了明细按明细来查下游戏
            if (rows.Count > 0)
            {
                strSql = @"select count(1) count from t_ydj_purchaseorder t1
                inner join t_ydj_poorderentry t2 on t1.fid=t2.fid
                where t1.fmainorgid=@fmainorgid and  t2.fsourceentryid in ('{0}') ".Fmt(string.Join("','", rows.ToList()));
            }
            else
            {
                strSql = @"select count(1) count from t_ydj_purchaseorder t1
                inner join t_ydj_poorderentry t2 on t1.fid=t2.fid
                where t1.fmainorgid=@fmainorgid and  t2.fsourcebillno in ('{0}') ".Fmt(string.Join("','", fbillno.Split(',').ToList()));
            }
            var sqlParam = new List<SqlParam>
                    {
                        new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
                    };
            using (var reader = this.Context.ExecuteReader(strSql, sqlParam))
            {
                if (reader.Read())
                    count = Convert.ToInt32(reader["count"]);
            }
            return count > 0 ? true : false;
        }

        /// <summary>
        /// 检查是否转单申请总且商品明细中是否存在发货经销商等于当前登录人【采购、出库、借货、退货、预留】 --*【预留】逻辑在另一个项目的OrderCommon
        /// </summary>
        /// <param name="orders"></param>
        /// <exception cref="BusinessException"></exception>
        public void CheckTransferOrderApprovingAndHasShipperAgent(DynamicObject[] orders)
        {
            //二级分销组织无法点击转单申请按钮，通过提交一级后一级走转单流程
            if (orders == null || orders.Count() == 0 || this.Context.IsSecondOrg)
            {
                return;
            }

            List<string> withoutApplyBillNoList = new List<string>();
            List<string> approvingBillNoList = new List<string>();
            List<string> withoutShipperEntryList = new List<string>();

            //获取主组织经销商
            var fentrys = orders.SelectMany(t => t["fentry"] as DynamicObjectCollection);
            var agentIds = fentrys.Select(t => Convert.ToString(t["fshipperagentid"]).Trim()).ToList();
            var mainAgents = (new AppService.Service.AgentService()).GetMainAgentIds(this.Context, agentIds);

            foreach (var order in orders)
            {
                if (IsTransferOrderApproving(order))
                {
                    approvingBillNoList.Add(order["fbillno"].ToString());
                }
                else if (IsNeedOrSaleTransferOrder(order))
                {
                    var entries = order["fentry"] as DynamicObjectCollection;
                    if (entries != null && entries.Count > 0)
                    {
                        if (IsNeedTransferOrder(order))
                        {
                            var hasApprovedEntry = entries.Count(x => !x["ftransferorderstatus"].IsNullOrEmptyOrWhiteSpace()) > 0;
                            if (hasApprovedEntry)
                            {
                                foreach (var item in entries)
                                {
                                    var agentId = Convert.ToString(item["fshipperagentid"]).Trim();
                                    var mainAgentId = "";
                                    mainAgents.TryGetValue(agentId, out mainAgentId);
                                    mainAgentId = mainAgentId.IsNullOrEmptyOrWhiteSpace() ? agentId : mainAgentId;
                                    //找不到对应主子经商时报错
                                    if ((mainAgents[item["fshipperagentid"].ToString().Trim()].IsNullOrEmptyOrWhiteSpace())
                                        && !withoutShipperEntryList.Contains(order["fbillno"].ToString()))
                                    {
                                        withoutShipperEntryList.Add(order["fbillno"].ToString());
                                    }
                                }
                            }
                            else
                            {
                                withoutApplyBillNoList.Add(order["fbillno"].ToString());
                            }
                        }
                    }
                }
            }
            List<string> msg = new List<string>();
            if (withoutApplyBillNoList.Count > 0)
            {
                msg.Add($"销售合同【{string.Join(",", withoutApplyBillNoList)}】已勾选需转单却尚未转单！");
            }
            if (approvingBillNoList.Count > 0)
            {
                msg.Add($"销售合同【{string.Join(",", approvingBillNoList)}】商品明细存在转单申请单为审批中！");
            }
            if (withoutShipperEntryList.Count > 0)
            {
                msg.Add($"销售合同【{string.Join(",", withoutShipperEntryList)}】{HasNotShipperAgentMessage}");
            }
            if (msg.Count > 0)
            {
                throw new BusinessException(string.Join("</br>", msg));
            }
        }


        /// <summary>
        /// 检查是否转单申请总且商品明细中是否存在发货经销商等于当前登录人【采购、出库、借货、退货、预留】 --*【预留】逻辑在另一个项目的OrderCommon
        /// </summary>
        /// <param name="orders"></param>
        /// <exception cref="BusinessException"></exception>
        public void CheckTransferOrderApprovingAndHasShipperAgentByEntry(DynamicObject[] orders, string[] entryIds)
        {
            //二级分销组织无法点击转单申请按钮，通过提交一级后一级走转单流程
            if (orders == null || orders.Count() == 0 || this.Context.IsSecondOrg)
            {
                return;
            }

            //获取主组织经销商
            var fentrys = orders.SelectMany(t => t["fentry"] as DynamicObjectCollection);
            if (entryIds == null || !entryIds.Any())
            {
                entryIds = fentrys.Where(x => !x["id"].IsNullOrEmptyOrWhiteSpace())?.Select(x => Convert.ToString(x["id"]))?.ToArray();
            }
            var agentIds = fentrys.Select(t => Convert.ToString(t["fshipperagentid"]).Trim()).ToList();
            var mainAgents = (new AppService.Service.AgentService()).GetMainAgentIds(this.Context, agentIds);
            List<string> msg = new List<string>();
            foreach (var order in orders)
            {
                if (IsTransferOrderApproving(order, entryIds, msg))
                {
                    //approvingBillNoList.Add(order["fbillno"].ToString());
                }
                else if (IsNeedOrSaleTransferOrder(order))
                {
                    var entries = order["fentry"] as DynamicObjectCollection;
                    if (entries != null && entries.Count > 0)
                    {
                        if (IsNeedTransferOrder(order))
                        {
                            var rows = 0;
                            foreach (var item in entries)
                            {
                                rows++;
                                if (!entryIds.Contains(Convert.ToString(item["id"])))
                                {
                                    continue;
                                }
                                var pName = JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]);
                                var ftransferorderstatus = 0;
                                if (!item["ftransferorderstatus"].IsNullOrEmptyOrWhiteSpace())
                                {
                                    ftransferorderstatus = Convert.ToInt32(item["ftransferorderstatus"]);
                                }

                                if (ftransferorderstatus == (int)TransferOrderStatus.Unspecified)
                                {
                                    msg.Add($"商品明细第{rows}行, 商品{pName} 已勾选需转单但尚末转单！不允许执行后续操作 ！");
                                }
                                else if (ftransferorderstatus == (int)TransferOrderStatus.Reject)//审批驳回
                                {
                                    msg.Add($"商品明细第{rows}行, 商品{pName} 已转单驳回但尚末重新转单,不允许执行后续操作 ！");
                                }
                                if (ftransferorderstatus == (int)TransferOrderStatus.Approved)
                                {
                                    var agentId = Convert.ToString(item["fshipperagentid"]).Trim();
                                    if (agentId == this.Context.Company) continue;
                                    var mainAgentId = "";
                                    mainAgents.TryGetValue(agentId, out mainAgentId);
                                    mainAgentId = mainAgentId.IsNullOrEmptyOrWhiteSpace() ? agentId : mainAgentId;
                                    //找不到对应主子经商时报错
                                    if (mainAgentId.IsNullOrEmptyOrWhiteSpace() || mainAgentId != this.Context.Company)
                                    // && !withoutShipperEntryList.Contains(order["fbillno"].ToString()))
                                    {
                                        //withoutShipperEntryList.Add(order["fbillno"].ToString());
                                        msg.Add($"商品明细第{rows}行,商品{pName}已转单且不为需要自行发货的商品！不允许执行后续操作 ！");
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (msg.Count > 0)
            {
                throw new BusinessException(string.Join("</br>", msg));
            }
        }

        /// <summary>
        /// 是否转单审核中
        /// </summary>
        /// <param name="obj"></param>
        /// <exception cref="BusinessException"></exception>
        public bool IsTransferOrderApproving(DynamicObject obj)
        {
            if (IsNeedTransferOrder(obj))
            {
                var entries = obj["fentry"] as DynamicObjectCollection;
                if (entries != null && entries.Count > 0)
                {
                    foreach (var entry in entries)
                    {
                        var status = entry["ftransferorderstatus"].ToString();
                        if (!status.IsNullOrEmptyOrWhiteSpace() && Convert.ToInt32(status) == (int)TransferOrderStatus.Approving)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 是否转单审核中
        /// </summary>
        /// <param name="obj"></param>
        /// <exception cref="BusinessException"></exception>
        public bool IsTransferOrderApproving(DynamicObject obj, string[] entryIds, List<string> error)
        {
            if (IsNeedTransferOrder(obj))
            {
                var entries = obj["fentry"] as DynamicObjectCollection;
                if (entries != null && entries.Count > 0)
                {
                    var rows = 0;
                    foreach (var entry in entries)
                    {
                        rows++;
                        if (!entryIds.Contains(Convert.ToString(entry["id"])))
                        {
                            continue;
                        }
                        var status = entry["ftransferorderstatus"].ToString();
                        if (!status.IsNullOrEmptyOrWhiteSpace() && Convert.ToInt32(status) == (int)TransferOrderStatus.Approving)
                        {
                            error.Add($"商品明细第{rows}行, 商品 {JNConvert.ToStringAndTrim((entry["fproductid_ref"] as DynamicObject)?["fname"])} 转单申请单为审批中！不允许执行后续操作 ！");
                        }
                    }

                    if (error.Count > 0) return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 是否转单审核中
        /// </summary>
        /// <param name="obj"></param>
        /// <exception cref="BusinessException"></exception>
        public bool IsTransferOrderApproving(DynamicObject obj, string[] entryIds)
        {
            if (IsNeedTransferOrder(obj))
            {
                var entries = obj["fentry"] as DynamicObjectCollection;
                if (entries != null && entries.Count > 0)
                {
                    var rows = 0;
                    foreach (var entry in entries)
                    {
                        rows++;
                        if (!entryIds.Contains(Convert.ToString(entry["id"])))
                        {
                            continue;
                        }
                        var status = entry["ftransferorderstatus"].ToString();
                        if (!status.IsNullOrEmptyOrWhiteSpace() && Convert.ToInt32(status) == (int)TransferOrderStatus.Approving)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        public void UpdateTransferOrder(DynamicObject[] funds)
        {
            if (funds == null || funds.Count() == 0)
            {
                return;
            }
            var fund = funds[0];
            var orderId = fund["fsourceid"].ToString();
            if (orderId.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var targetFormId = "ydj_order";
            if (fund["fsourceformid"].ToString().EqualsIgnoreCase(targetFormId))
            {
                var order = this.Context.LoadBizDataById(targetFormId, orderId);
                if (order != null)
                {
                    this.UpdateTransferOrder(order);
                }
            }
        }

        /// <summary>
        /// 接单方:更新转单申请单【已收款金额】
        /// </summary>
        /// <param name="order">销售合同实体</param>
        private void UpdateTransferOrder(DynamicObject order)
        {
            if (!IsNeedTransferOrder(order))
            {
                return;
            }

            var fentries = order["fentry"] as DynamicObjectCollection;
            if (fentries == null || fentries.Count == 0)
            {
                return;
            }
            var fbillno = order["fbillno"].ToString(); //销售合同单据号  

            var shipperOrderList = new List<DynamicObject>();
            var applyList = new List<DynamicObject>();

            //1.更新当前接单方的转单申请单 
            var receiverApplyList = this.Context.LoadBizDataByFilter(TransferOrderApplyFormId, $"fsourcenumber='{fbillno}'").ToList();
            if (receiverApplyList != null && receiverApplyList.Count > 0)
            {
                applyList.AddRange(receiverApplyList);
            }

            //2.更新送货方的转单申请单 
            foreach (var entry in fentries)
            {
                var shipperAgentId = entry["fshipperagentid"].ToString();//发货方经销商
                if (!shipperAgentId.IsNullOrEmptyOrWhiteSpace())
                {
                    var shipperAgentCtx = this.Context.CreateAgentDBContext(shipperAgentId);
                    if (shipperAgentCtx != null)
                    {
                        var shipperOrder = shipperAgentCtx.LoadBizDataByFilter(OrderFormId, $"freceivercontractnumber='{fbillno}'").FirstOrDefault();
                        if (shipperOrder != null)
                        {
                            shipperOrderList.Add(shipperOrder);
                            var shipperOrderNo = shipperOrder["fbillno"].ToString();//发货方销售合同单据编号 
                            var shipperApplyList = shipperAgentCtx.LoadBizDataByFilter(TransferOrderApplyFormId, $"fsourcenumber='{shipperOrderNo}'").ToList();
                            if (shipperApplyList != null && shipperApplyList.Count > 0)
                            {
                                applyList.AddRange(shipperApplyList);
                            }
                        }
                    }
                }
            }

            //批量更新送货放合同
            //if (shipperOrderList.Count > 0)
            //{
            //    foreach (var shipperOrder in shipperOrderList)
            //    {
            //        shipperOrder["freceivabletobeconfirmed"] = order["freceivabletobeconfirmed"];//收款待确认
            //        shipperOrder["freceivable"] = order["freceivable"];//确认收款【已收款金额】
            //        shipperOrder["funreceived"] = order["funreceived"]; //未收款
            //        shipperOrder["factrefundamount"] = order["factrefundamount"];//实退金额
            //    }
            //    this.Context.SaveBizData(OrderFormId, shipperOrderList);
            //}
            //批量更新接单方与送货方的转单申请单
            if (applyList.Count > 0)
            {
                foreach (var apply in applyList)
                {
                    apply["freceivedamount"] = order["freceivable"];//确认收款【已收款金额】
                    apply["fshipperdate"] = DateTime.Now.ToString();
                }
                this.Context.SaveBizData(TransferOrderApplyFormId, applyList);
            }
        }

        /// <summary>
        /// 验证合同下推采购
        /// </summary>
        /// <param name="profileService"></param>
        /// <param name="dataEntity"></param>
        public void CheckPurOrder(ISystemProfile profileService, DynamicObject dataEntity)
        {
            string message = "";
            int errcount = 0;
            var objList = dataEntity["fentry"] as DynamicObjectCollection;
            //加载全部商品ID
            var prodids = objList.Where(x=>!Convert.ToBoolean(x["fisfromfirstinventory"])).Select(x => x["fproductid"].ToString()).ToList();

            #region 状态是否有效

            if (dataEntity["fclosestate"].ToString() == "1")
            {
                throw new BusinessException("合同关闭状态为已关闭，采购失败！");
            }

            #endregion


            //2024-03-15确认，下周一上线回到，先不允许这段逻辑生效,还原旧逻辑
            #region 审校判断
            if (dataEntity["fbilltype_ref"] == null)
            {
                var htmlForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, OrderFormId);
                var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                refObjMgr?.Load(this.Context, new object[] { dataEntity }, true, htmlForm, new List<string> { "fbilltype" });
            }
            if (Convert.ToString((dataEntity["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("v6定制柜合同"))
            {
                if (!Convert.ToBoolean(dataEntity["fomsservice"]))
                {
                    //是否审核
                    if (dataEntity["fstatus"].ToString() != "E")
                    {
                        errcount++;
                        message += errcount + "、采购失败：销售合同必须是已审核状态！\r\n";
                    }
                }
            }
            else
            {
                //是否审核
                if (dataEntity["fstatus"].ToString() != "E")
                {
                    errcount++;
                    message += errcount + "、采购失败：销售合同必须是已审核状态！\r\n";
                }
            }

            #endregion

            #region 至少要有一行商品明细商品数量大于已采购数量
            //允许采购数量大于销售数量
            var isfsuperpinpurchase = profileService.GetSystemParameter(Context, "pur_systemparam", "fsuperpinpurchase", false);

            if (!isfsuperpinpurchase)
            {
                var isPur = false;
                foreach (var item in objList)
                {
                    //判断至少要有一行商品明细商品数量大于已采购数量
                    if (Convert.ToInt32(item["fqty"]) > Convert.ToInt32(item["fpurqty"]))
                    {
                        isPur = true;
                        break;
                    }
                }
                if (!isPur)
                {
                    errcount++;
                    message += errcount + "、 至少要有一行商品明细商品数量大于已采购数量（检查是否已下推采购订单）！\r\n";
                }
            }

            #endregion

            #region 至少要有一行未停购的商品明细
            if (prodids.Count > 0)
            {
                string sql = $"select fpublishcid,fpublishcid_pid,fid from T_BD_MATERIAL mtl where mtl.fid in ({string.Join(",", prodids.Select(s => $"'{s}'"))}) and mtl.fendpurchase<>'1'";
                var dbService = this.Context.Container.GetService<IDBService>();
                var dynObjs = dbService.ExecuteDynamicObject(this.Context, sql);
                if (dynObjs.Count == 0)
                {
                    errcount++;
                    message += errcount + "、至少要有一行未停购的商品明细\r\n";
                }
            }

            #endregion

            #region 焕新订单校验商城交易流水号以及结算状态
            if (Convert.ToBoolean(dataEntity["frenewalflag"])) {
                var orderData = this.Context.LoadBizBillHeadDataById("ydj_order", dataEntity["id"].ToString(), "fmembershiptranid,fsettlprogress");
                if (orderData != null) {
                    if (orderData["fmembershiptranid"].ToString().IsNullOrEmptyOrWhiteSpace()) {
                        errcount++;
                        message += errcount + "因网络波动导致错误，请进行错误重试！\r\n";
                    }
                    if (orderData["fsettlprogress"] != null && orderData["fsettlprogress"].ToString() != "2") {
                        errcount++;
                        message += errcount + "、因网络波动导致错误，请进行错误重试！\r\n";
                    }
                }
            }
            #endregion

            #region 验证辅助属性
            var checkentry =  objList.Where(x => !Convert.ToBoolean(x["funstdtype"])&& !Convert.ToBoolean(x["fisoutspot"]) && !string.IsNullOrWhiteSpace(x["fattrinfo"].ToString())).ToList();
            var checkpids = checkentry.Select(x => x["fproductid"].ToString()).Distinct().ToList();
            var checkpeopids = checkentry.Select(x => x["fattrinfo"].ToString()).Distinct().ToList();
            string checkpidsSql = $@"select fid,fmaterialid,fnumber,fname from t_bd_auxpropvalue where fmaterialid in ('{string.Join("','", checkpids)}') and fforbidstatus='0' and fid in('{string.Join("','", checkpeopids)}') group by fid,fmaterialid,fnumber,fname ";
            var fattrinfoeData = this.Context.ExecuteDynamicObject(checkpidsSql, null).ToList();
            var _auxprop = new Dictionary<string, DynamicObject>();
            foreach (var item in fattrinfoeData)
            {
                string key=$"{item["fmaterialid"]}_{item["fid"]}";
                if (!_auxprop.ContainsKey(key)){
                    _auxprop[key] = item;
                }
            }
            string datasql = $@"select a.fid as producid, c.fpropid,d.fname as fpropname,c.fpropvalueid,c.fpropvalueid_txt  from T_BD_MATERIAL a with(nolock)
                                inner join t_sel_range  b with(nolock) on a.fselcategoryid=b.fselcategoryid
                                inner join t_sel_rangeentry c with(nolock) on b.fid=c.fid  
                                inner join t_sel_prop d with(nolock) on c.fpropid=d.fid
                                where a.fid in ('{string.Join("','", checkpids)}')";
            var rangeData = this.Context.ExecuteDynamicObject(datasql, null).ToList().GroupBy(x=>x["producid"].ToString()).ToDictionary(x=>x.Key,x=>x.ToList());
            foreach (var item in checkentry)
            {
                if (_auxprop.TryGetValue($"{item["fproductid"].ToString().Trim()}_{item["fattrinfo"].ToString().Trim()}", out var obj)){
                    Dictionary<string, string> dictionary = obj["fnumber"].ToString().Split(',') .Select(part => part.Split(':')).ToDictionary(split => split[0], split => split[1]);
                    if (rangeData.TryGetValue(item["fproductid"].ToString(),out var dynamics)) {
                        foreach (var _ranData in dynamics)
                        {
                            if (dictionary.TryGetValue(_ranData["fpropid"].ToString(), out string fpropvalueid))
                            {
                                var propValues = _ranData["fpropvalueid"].ToString().Split(',');
                                var propValueNames = _ranData["fpropvalueid_txt"].ToString().Split(',');
                                if (!propValues.Contains(fpropvalueid))
                                {
                                    message += $"销售合同【{dataEntity["fbillno"]}】第{item["Fseq"]}行商品【{Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"])}】,辅助属性为非标属性,请反审核销售合同非标审批通过后在转采购！";
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            #endregion

            if (message != "")
                throw new BusinessException(message);
        }

        /// <summary>
        /// 是否需转单
        /// </summary>
        /// <returns></returns>
        private bool IsNeedTransferOrder(DynamicObject dataEntity)
        {
            return Convert.ToBoolean(dataEntity["fneedtransferorder"]);
        }

        /// <summary>
        /// 获取销售合同销售转单单据类型ID
        /// </summary>
        /// <returns></returns>
        public string GetSaleTransferOrderBillTypeId()
        {
            //当前销售合同的单据类型是否【销售转单】类型 
            var billTypeObj = this.Context.GetBillTypeByBizObject(OrderFormId, SaleTransferOrderBillType);
            return billTypeObj.fid;
        }

        /// <summary>
        /// 是否销售转单
        /// </summary>
        /// <returns></returns>
        public bool IsSaleTransferOrder(DynamicObject order)
        {
            //当前销售合同的单据类型是否【销售转单】类型 
            var billTypeObj = this.Context.GetBillTypeByBizObject(OrderFormId, SaleTransferOrderBillType);
            if (billTypeObj != null && billTypeObj.fid.EqualsIgnoreCase(Convert.ToString(order["fbilltype"])))
            {
                return true;
            }
            //判断是否通过转单申请单审核系统创建的【销售转单】《销售合同》
            return !order["fissaletransferorder"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(order["fissaletransferorder"]);
        }

        /// <summary>
        /// 是需转单或销售转单
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public bool IsNeedOrSaleTransferOrder(DynamicObject order)
        {
            return IsNeedTransferOrder(order) || IsSaleTransferOrder(order);
        }

        /// <summary>
        /// 获取销售合同的销售员
        /// </summary>
        /// <param name="billNo"></param>
        /// <returns></returns>
        public List<string> GetOrderStaffs(string billNo)
        {
            var staffs = new List<string>();
            var staffSql = @"select c.fname from t_ydj_order a
                        inner join t_ydj_orderduty b on b.fid=a.fid
                        left join t_bd_staff c on c.fid=b.fdutyid 
                        where a.fmainorgid='{0}' and a.fbillno='{1}' ".Fmt(this.Context.Company, billNo);
            var _dbService = this.Context.Container.GetService<IDBService>();
            var queryData = _dbService.ExecuteDataTable(this.Context, staffSql);
            if (queryData != null || queryData.Rows.Count > 0)
            {
                foreach (DataRow row in queryData.Rows)
                {
                    var staffName = row["fname"].ToString();
                    if (!string.IsNullOrEmpty(staffName) && !staffs.Contains(staffName))
                    {
                        staffs.Add(staffName);
                    }
                }
            }
            queryData = null;

            return staffs;
        }

        /// <summary>
        /// 是否勾选中线下转单
        /// </summary>
        /// <returns></returns>
        public bool CheckIsofflineTrans(List<string> fsourcenentryids)
        {
            string strSql = @"select t1.fisoffline from t_ydj_transferorderapply t1 with(nolock)
                join t_transferorderapply_pro t2 with(nolock) on t1.fid = t2.fid
                where t2.fsourcenentryid in ('{0}') and t1.fcancelstatus=0".Fmt(string.Join("','", fsourcenentryids));
            int i = 0;
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    i++;
                    if (Convert.ToString(dr["fisoffline"]) != "1")
                    {
                        return false;
                    }
                }
            }
            if (i == 0)
                return false;//无数据应为false 审批驳回的会自动作废该申请单

            if (i != fsourcenentryids.Count())
                return false;

            return true;
        }

        /// <summary>
        /// 处理订单属性字段
        /// 只有标准销售合同和v6定制柜订单类型，并且项目编码不为空才会走逻辑
        /// </summary>
        /// <param name="dataEntitys"></param>
        public void UpdateProjectAttr(DynamicObject[] dataEntitys)
        {
            int orderAttr = 3;
            //以下更新都是不管你自己有没有订单属性，都会重新检查一遍再更新
            var htmlForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, OrderFormId);
            var productHtmlForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_product");
            var projectinfoForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_projectinfo");
            // 加载数据
            var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, dataEntitys, true, htmlForm, new List<string> { "fbilltype" });
            //操作数据库
            var dm = this.Context.Container.GetService<IDataManager>();
            //var projectinfoForm = this.MetaModelService.LoadFormModel(this.Context, OrderFormId);
            foreach (var item in dataEntitys)
            {
                string projectnumber = Convert.ToString(item["fprojectnumber"]);
                string orderattr = Convert.ToString(item["forderattr"]);
                bool istranspurqty = false;
                if (!string.IsNullOrWhiteSpace(projectnumber))
                {
                    var entry = item["fentry"] as DynamicObjectCollection;
                    foreach (var entryItem in entry)
                    {
                        if (Convert.ToDecimal(entryItem["ftranspurqty"]) > 0)
                        {
                            istranspurqty = true;
                            //转过采购就不允许更新
                            break;
                        }
                    }
                    if (istranspurqty) continue;

                    if (Convert.ToString((item["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("标准销售合同"))
                    {
                        entry = item["fentry"] as DynamicObjectCollection;

                        var products = this.Context.LoadBizDataById("ydj_product", entry.Select(a => Convert.ToString(a["fproductid"])).ToList());
                        refObjMgr?.Load(this.Context, products.ToArray(), true, productHtmlForm, new List<string> { "fbrand" });


                        bool exists = false;
                        foreach (var entryItem in entry)
                        {
                            if (Convert.ToDecimal(entryItem["ftranspurqty"]) > 0)
                            {
                                orderattr = "";
                                //转过采购就不允许更新
                                break;
                            }
                            var productItem = products.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(entryItem["fproductid"]))).FirstOrDefault();
                            if (Convert.ToString((productItem["fbrandid_ref"] as DynamicObject)?["fname"]).Equals("V6传统"))
                            {
                                exists = true;
                                break;
                            }
                        }
                        //只要有一行三维家的虚拟商品，则去找其他项目信息的合同
                        if (exists)
                        {
                            var orders = this.Context.LoadBizDataByFilter(OrderFormId, $" fprojectnumber='{projectnumber}'");
                            if (orders.Count == 0)
                                continue;

                            List<DynamicObject> updItem = new List<DynamicObject>();
                            bool canchangefilter = false;//是否满足更新条件
                            refObjMgr?.Load(this.Context, orders.ToArray(), true, htmlForm, new List<string> { "fbilltype" });
                            foreach (var orderItem in orders)
                            {
                                if (Convert.ToString((orderItem["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("v6定制柜合同"))
                                {
                                    bool canUpd = true;//是否允许更新数据
                                    //要判断明细行状态，明细行状态都满足待提交、待接单才可以更新
                                    var orderItemEntry = orderItem["fentry"] as DynamicObjectCollection;
                                    foreach (var _item in orderItemEntry)
                                    {
                                        if (!Convert.ToString(_item["fomsprogress"]).Equals("5") && !Convert.ToString(_item["fomsprogress"]).Equals("10") && !Convert.ToString(_item["fomsprogress"]).Equals("-1"))
                                        {
                                            canUpd = false;
                                        }
                                    }
                                    if (canUpd)
                                    {
                                        updItem.Add(orderItem);
                                    }
                                    canchangefilter = true;
                                    continue;
                                }
                            }
                            //标准销售合同的话，要有v6单据，才算是大家居

                            if (canchangefilter)
                            {
                                if (updItem.Count > 0)
                                {
                                    //关联合同打上大家居标识
                                    updItem.ForEach(c =>
                                    {
                                        c["forderattr"] = orderAttr;
                                        var _e = c["fentry"] as DynamicObjectCollection;
                                        foreach (var _obj in _e)
                                        {
                                            _obj["forderattr_e"] = orderAttr;
                                        }
                                    });
                                    item["forderattr"] = orderAttr;
                                    var _entry = item["fentry"] as DynamicObjectCollection;
                                    foreach (var _entryIten in _entry)
                                    {
                                        _entryIten["forderattr_e"] = orderAttr;
                                    }
                                }
                            }
                            else
                            {
                                //清空标识
                                //关联合同打上大家居标识
                                updItem.ForEach(c =>
                                {
                                    c["forderattr"] = "";
                                    c["factivenumber"] = "";
                                    var _e = c["fentry"] as DynamicObjectCollection;
                                    foreach (var _obj in _e)
                                    {
                                        _obj["forderattr_e"] = "";
                                    }
                                });
                                item["forderattr"] = "";
                                item["factivenumber"] = "";
                                var _entry = item["fentry"] as DynamicObjectCollection;
                                foreach (var _entryIten in _entry)
                                {
                                    _entryIten["forderattr_e"] = "";
                                }
                            }
                            var projectItem = this.Context.LoadBizDataById("ydj_projectinfo", projectnumber);
                            projectItem["forderattr"] = item["forderattr"];
                            projectItem["factivenumber"] = item["factivenumber"];
                            //初始化上下文
                            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                            dm.Save(updItem);
                            var projectinfoFormType = projectinfoForm.GetDynamicObjectType(this.Context);

                            dm.InitDbContext(this.Context, projectinfoFormType);
                            dm.Save(projectItem);
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(orderattr))
                            {
                                //有打标记，但没有v6业务的虚拟商品，需要清空标记
                                item["forderattr"] = "";
                                var _entry = item["fentry"] as DynamicObjectCollection;
                                foreach (var _entryIten in _entry)
                                {
                                    _entryIten["forderattr_e"] = "";
                                }
                                var projectItem = this.Context.LoadBizDataById("ydj_projectinfo", projectnumber);
                                projectItem["forderattr"] = "";
                                var projectinfoFormType = projectinfoForm.GetDynamicObjectType(this.Context);
                                dm.InitDbContext(this.Context, projectinfoFormType);
                                dm.Save(projectItem);
                            }
                        }
                    }
                    else if (Convert.ToString((item["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("v6定制柜合同"))
                    {
                        //v6单据，默认认为有虚拟商品
                        {//bug
                            string sql = $@"select a.fid,a.fbillno,d.fname,b.ftranspurqty,a.fmainorgid,b.fomsprogress from T_YDJ_ORDER a with(nolock)
                                                inner join T_YDJ_ORDERENTRY b with(nolock) on a.fid = b.fid
                                                inner join T_BD_MATERIAL c with(nolock) on b.fproductid = c.fid
                                                inner join T_YDJ_BRAND d with(nolock) on c.fbrandid = d.fid
                                                where a.fprojectnumber = '{projectnumber}' and a.fmainorgid = '{this.Context.Company}' and a.fcancelstatus=0 
                                                and a.fbilltype in (select fid from T_BD_BILLTYPE with(nolock) where fname= '标准销售合同') ";
                            var orders = this.Context.ExecuteDynamicObject(sql, null);

                            List<string> ids = new List<string>();
                            foreach (var tmp in orders)
                            {
                                if (Convert.ToDecimal(tmp["ftranspurqty"]) > 0)
                                {
                                    continue;
                                }
                                if (Convert.ToString(tmp["fname"]).Equals("V6传统"))
                                {
                                    ids.Add(Convert.ToString(tmp["fid"]));
                                }
                            }
                            List<DynamicObject> updItem = new List<DynamicObject>();
                            //v6定制柜合同的话，要有标准销售合同单据，才算是大家居
                            if (orders.Count > 0)
                            {
                                updItem = this.Context.LoadBizDataById("ydj_order", ids);
                                //关联合同打上大家居标识
                                updItem.ForEach(c =>
                                {
                                    c["forderattr"] = orderAttr;
                                    var _e = c["fentry"] as DynamicObjectCollection;
                                    foreach (var _obj in _e)
                                    {
                                        _obj["forderattr_e"] = orderAttr;
                                    }
                                });
                                item["forderattr"] = orderAttr;
                                var _entry = item["fentry"] as DynamicObjectCollection;
                                foreach (var _entryIten in _entry)
                                {
                                    _entryIten["forderattr_e"] = orderAttr;

                                }

                                //初始化上下文
                                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                                dm.Save(updItem);
                            }
                            else
                            {
                                //清空标识
                                item["forderattr"] = "";
                                var _entry = item["fentry"] as DynamicObjectCollection;
                                foreach (var _entryIten in _entry)
                                {
                                    _entryIten["forderattr_e"] = "";
                                }
                            }

                            var projectItem = this.Context.LoadBizDataById("ydj_projectinfo", projectnumber);
                            projectItem["forderattr"] = item["forderattr"];
                            var projectinfoFormType = projectinfoForm.GetDynamicObjectType(this.Context);
                            dm.InitDbContext(this.Context, projectinfoFormType);
                            dm.Save(projectItem);

                        }
                    }
                }

            }
            //初始化上下文
            //dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            //dm.Save(dataEntitys);
            foreach (var item in dataEntitys)
            {
                string projectnumber = Convert.ToString(item["fprojectnumber"]);
                string orderattr = Convert.ToString(item["forderattr"]);
                if (!string.IsNullOrWhiteSpace(projectnumber))
                {
                    this.DealOrderAttr(projectnumber, orderattr, Convert.ToString(item["id"]));
                }
            }
        }



        private void DealOrderAttr(string projectnumber, string orderAttr, string id)
        {
            var dm = this.Context.Container.GetService<IDataManager>();
            int _orderAttr = 3;
            /*满足更新的订单
             * 项目相同
                v6条件:明细行状态
                标准条件：没下采购，商品有"V6传统"品牌
            */
            /*不符合条件的订单
             * 项目相同
                v6条件:明细行状态不满足
                标准条件：没下采购，商品有"V6传统"品牌
             */
            string filter = $" fprojectnumber='{projectnumber}' AND fbilltype in (select fid from T_BD_BILLTYPE with(nolock) where fname='标准销售合同' or fname='v6定制柜合同') AND fid<>'{id}'";
            //if (!string.IsNullOrWhiteSpace(orderAttr))
            //{
            //    //如有是赋值，就查为空的
            //    filter += " AND forderattr='' ";
            //}
            //else
            //{
            //    //如果没值，就查不为空的
            //    filter += " AND forderattr<>'' ";
            //}
            var htmlForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, OrderFormId);
            var orders = this.Context.LoadBizDataByFilter(OrderFormId, filter);
            var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, orders.ToArray(), true, htmlForm, new List<string> { "fbilltype" });

            List<DynamicObject> matchV6 = new List<DynamicObject>();
            List<DynamicObject> matchStand = new List<DynamicObject>();

            var gp = orders.GroupBy(a => Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"])).ToList();
            foreach (var gpItem in gp)
            {
                var _orders = orders.Where(a => Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"]).Equals(gpItem.Key)).ToList();
                if (gpItem.Key.Equals("v6定制柜合同"))
                {
                    foreach (var v6Item in _orders)
                    {
                        bool canUpd = true;//是否允许更新数据
                        var orderItemEntry = v6Item["fentry"] as DynamicObjectCollection;
                        foreach (var _item in orderItemEntry)
                        {
                            if (!Convert.ToString(_item["fomsprogress"]).Equals("5") && !Convert.ToString(_item["fomsprogress"]).Equals("10") && !Convert.ToString(_item["fomsprogress"]).Equals("-1"))
                            {
                                canUpd = false;
                            }
                        }
                        if (canUpd)
                        {
                            matchV6.Add(v6Item);
                        }
                    }
                }
                else
                {
                    //bug
                    string sql = $@"select a.fid,a.fbillno,d.fname,b.ftranspurqty,a.fmainorgid,b.fomsprogress from T_YDJ_ORDER a with(nolock)
                                                inner join T_YDJ_ORDERENTRY b with(nolock) on a.fid = b.fid
                                                inner join T_BD_MATERIAL c with(nolock) on b.fproductid = c.fid
                                                inner join T_YDJ_BRAND d with(nolock) on c.fbrandid = d.fid
                                                where a.fprojectnumber = '{projectnumber}' and a.fmainorgid = '{this.Context.Company}' and a.fcancelstatus=0 
                                                and a.fbilltype in (select fid from T_BD_BILLTYPE with(nolock) where fname= '标准销售合同')  AND a.fid<>'{id}'";
                    var standOrders = this.Context.ExecuteDynamicObject(sql, null);

                    List<string> ids = new List<string>();
                    foreach (var item in standOrders)
                    {
                        if (Convert.ToDecimal(item["ftranspurqty"]) > 0)
                        {
                            continue;
                        }
                        if (Convert.ToString(item["fname"]).Equals("V6传统"))
                        {
                            ids.Add(Convert.ToString(item["fid"]));
                        }
                    }
                    var objs = this.Context.LoadBizDataById("ydj_order", ids);

                    foreach (var item in objs)
                    {
                        matchStand.Add(item);
                    }
                }

            }

            if (matchV6.Count == 0 || matchStand.Count == 0)
            {
                //有其中一个，查不到有符合条件的单据，则有数据的直接清空
                matchV6.ForEach(c =>
                {
                    c["forderattr"] = "";
                    c["factivenumber"] = "";
                    var _e = c["fentry"] as DynamicObjectCollection;
                    foreach (var _obj in _e)
                    {
                        _obj["forderattr_e"] = "";
                    }
                });
                matchStand.ForEach(c =>
                {
                    c["forderattr"] = "";
                    c["factivenumber"] = "";
                    var _e = c["fentry"] as DynamicObjectCollection;
                    foreach (var _obj in _e)
                    {
                        _obj["forderattr_e"] = "";
                    }
                });
            }
            if (matchV6.Count > 0 && matchStand.Count > 0)
            {
                matchV6.ForEach(c =>
                {
                    c["forderattr"] = _orderAttr;
                    var _e = c["fentry"] as DynamicObjectCollection;
                    foreach (var _obj in _e)
                    {
                        _obj["forderattr_e"] = _orderAttr;
                    }
                });
                matchStand.ForEach(c =>
                {
                    c["forderattr"] = _orderAttr;
                    var _e = c["fentry"] as DynamicObjectCollection;
                    foreach (var _obj in _e)
                    {
                        _obj["forderattr_e"] = _orderAttr;
                    }
                });
                var projectItem = this.Context.LoadBizDataById("ydj_projectinfo", projectnumber);
                projectItem["forderattr"] = _orderAttr;
                var projectinfoForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_projectinfo");
                projectItem["factivenumber"] = matchStand[0]["factivenumber"];
                var projectinfoFormType = projectinfoForm.GetDynamicObjectType(this.Context);
                //操作数据库

                dm.InitDbContext(this.Context, projectinfoFormType);
                dm.Save(projectItem);
            }

            //初始化上下文
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            dm.Save(matchStand);
            dm.Save(matchV6);
        }
    }


    public class Row
    {
        public string Id { get; set; }
    }
}
